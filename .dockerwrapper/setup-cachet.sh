#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Setting up Cachet Dashboard...${NC}"

# Stop any existing containers
echo -e "${YELLOW}Stopping any existing Cachet containers...${NC}"
docker compose -f .dockerwrapper/docker-compose.cachet.yml down

# Start Cachet
echo -e "${YELLOW}Starting TurdParty Cachet Dashboard...${NC}"
docker compose -f .dockerwrapper/docker-compose.cachet.yml up -d

echo -e "${GREEN}Cachet Dashboard is starting at http://localhost:3501${NC}"
echo -e "${GREEN}It may take a minute for the service to be fully available.${NC}"
echo -e "${YELLOW}You will need to complete the setup through the web interface.${NC}"
echo -e "${YELLOW}Go to http://localhost:3501/setup to configure Cachet.${NC}"
echo ""
echo -e "${YELLOW}Waiting for <PERSON><PERSON><PERSON> to be fully initialized...${NC}"

# Wait for <PERSON><PERSON><PERSON> to be ready
ATTEMPTS=0
MAX_ATTEMPTS=30
until curl -s -o /dev/null -w "%{http_code}" http://localhost:3501/api/v1/ping | grep -q "200" || [ $ATTEMPTS -eq $MAX_ATTEMPTS ]; do
    ATTEMPTS=$((ATTEMPTS + 1))
    echo -e "${YELLOW}Waiting for Cachet to be ready... attempt $ATTEMPTS of $MAX_ATTEMPTS${NC}"
    sleep 5
done

if [ $ATTEMPTS -eq $MAX_ATTEMPTS ]; then
    echo -e "${YELLOW}Cachet is taking longer than expected to start. It may still be initializing.${NC}"
    echo -e "${YELLOW}You can check the status with: docker logs turdparty_cachet${NC}"
else
    echo -e "${GREEN}Cachet is now ready!${NC}"
    echo -e "${GREEN}You can access the dashboard at http://localhost:3501${NC}"
fi

# Generate a random API token for the update script
echo -e "${YELLOW}Generating API token for the update script...${NC}"
API_TOKEN=$(openssl rand -hex 32)
echo -e "${GREEN}API token generated: $API_TOKEN${NC}"

# Update the update-cachet-status.sh script with the API token
sed -i "s|CACHET_API_TOKEN=\"\"|CACHET_API_TOKEN=\"$API_TOKEN\"|g" .dockerwrapper/update-cachet-status.sh
echo -e "${GREEN}Updated update-cachet-status.sh with API token${NC}"

echo -e "${YELLOW}Note: You will need to create an API token in the Cachet dashboard with this value:${NC}"
echo -e "${GREEN}$API_TOKEN${NC}"
echo -e "${YELLOW}Go to Dashboard > Settings > API Tokens and create a new token with this value.${NC}"
